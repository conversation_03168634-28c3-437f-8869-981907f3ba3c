/// Approximate token estimation, assuming 4 characters per token.
pub fn estimate_tokens(text: &str) -> usize {
    let len = text.chars().count();
    if len == 0 {
        0
    } else {
        // At least 1 token for any non-empty string
        std::cmp::max(1, len / 4)
    }
}

pub fn estimate_tokens_format_nicely(text: &str) -> String {
    let estimated_tokens = estimate_tokens(text);
    if estimated_tokens > 1_000_000 {
        format!("{}m", estimated_tokens / 1_000_000)
    } else if estimated_tokens > 1_000 {
        format!("{}k", estimated_tokens / 1_000)
    } else {
        estimated_tokens.to_string()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_estimate_tokens_format_nicely() {
        assert_eq!(estimate_tokens_format_nicely("hello world"), "2");
        assert_eq!(
            estimate_tokens_format_nicely(&"hello world".repeat(1000)),
            "2k"
        );
        assert_eq!(
            estimate_tokens_format_nicely(&"hello world".repeat(1_000_000)),
            "2m"
        );
    }
}
