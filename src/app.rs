use crate::cli::cli_args::Cli;
use crate::config::user_agent_config::DimaAgentConfig;
use crate::provider_models::llm_provider_builder::build_llm_provider;
use crate::provider_models::llm_provider_type::LlmProviderType;
use crate::session_management;
use crate::session_management::global_state::GlobalAppState;
use llm::chat::ChatMessage;
use llm::LLMProvider;
use session_management::global_state::load_global_state;
use session_management::session_manager::save_session;
use std::path::PathBuf;
use std::sync::Arc;
use std::time::Instant;

#[derive(Clone)]
pub struct SystemPrompt {
    pub content: String,
    pub with_custom_user_prompt: bool,
    pub workspace_content: String,
}

pub struct App {
    conversation_messages: Vec<ChatMessage>,
    pub should_quit: bool,
    pub cli_args: Cli,
    pub system_prompt: SystemPrompt,
    pub llm: Arc<dyn LLMProvider>,
    pub working_dir: PathBuf,
    current_model: LlmProviderType,
    pub dima_config: Option<DimaAgentConfig>,
    code_was_changed: bool,
    pub session_id: String,
    pub session_allowed_bash_commands: Vec<String>,
    pub session_denied_bash_commands: Vec<String>,
    /// Contains the last reported API call total used tokens including prompt and completion. Stored in session.
    last_response_usage_tokens: Option<usize>,
    pub interrupt_loop_to_user_prompt: bool,
    pub active_api_key_index: usize,
    pub consecutive_429_errors: usize,
    pub last_key_switch_time: Option<Instant>,
    pub yolo_mode: bool,
}

pub struct AppBuilder {
    conversation_messages: Vec<ChatMessage>,
    should_quit: bool,
    cli_args: Option<Cli>,
    system_prompt: Option<SystemPrompt>,
    llm: Option<Arc<dyn LLMProvider>>,
    working_dir: Option<PathBuf>,
    current_model: Option<LlmProviderType>,
    dima_config: Option<DimaAgentConfig>,
    code_was_changed: bool,
    session_id: Option<String>,
    session_allowed_bash_commands: Vec<String>,
    session_denied_bash_commands: Vec<String>,
    last_response_usage_tokens: Option<usize>,
    interrupt_loop_to_user_prompt: bool,
    active_api_key_index: usize,
    consecutive_429_errors: usize,
    last_key_switch_time: Option<Instant>,
    yolo_mode: bool,
}

impl AppBuilder {
    pub fn new() -> Self {
        Self {
            conversation_messages: vec![],
            should_quit: false,
            cli_args: None,
            system_prompt: None,
            llm: None,
            working_dir: None,
            current_model: None,
            dima_config: None,
            code_was_changed: false,
            session_id: None,
            session_allowed_bash_commands: vec![],
            session_denied_bash_commands: vec![],
            last_response_usage_tokens: None,
            interrupt_loop_to_user_prompt: false,
            active_api_key_index: 0,
            consecutive_429_errors: 0,
            last_key_switch_time: None,
            yolo_mode: false,
        }
    }

    // Setter methods
    #[must_use]
    pub fn cli_args(mut self, args: Cli) -> Self {
        self.cli_args = Some(args);
        self
    }

    #[must_use]
    pub fn system_prompt(mut self, prompt: SystemPrompt) -> Self {
        self.system_prompt = Some(prompt);
        self
    }

    #[must_use]
    pub fn llm(mut self, llm: Box<dyn LLMProvider>) -> Self {
        self.llm = Some(Arc::from(llm));
        self
    }

    #[must_use]
    pub fn working_dir(mut self, cwd: PathBuf) -> Self {
        self.working_dir = Some(cwd);
        self
    }

    #[must_use]
    pub fn current_model(mut self, model: LlmProviderType) -> Self {
        self.current_model = Some(model);
        self
    }

    #[must_use]
    pub fn dima_config(mut self, config: Option<DimaAgentConfig>) -> Self {
        self.dima_config = config;
        self
    }

    #[must_use]
    pub fn session_id(mut self, id: String) -> Self {
        self.session_id = Some(id);
        self
    }

    /// Panics if a required field is not set.
    #[allow(clippy::too_many_arguments)]
    pub fn build(self) -> Result<App, &'static str> {
        Ok(App {
            conversation_messages: self.conversation_messages,
            should_quit: self.should_quit,
            cli_args: self.cli_args.ok_or("cli_args is required")?,
            system_prompt: self.system_prompt.ok_or("system_prompt is required")?,
            llm: self.llm.ok_or("llm is required")?,
            working_dir: self.working_dir.ok_or("working_dir is required")?,
            current_model: self.current_model.ok_or("current_model is required")?,
            dima_config: self.dima_config,
            code_was_changed: self.code_was_changed,
            session_id: self.session_id.ok_or("session_id is required")?,
            session_allowed_bash_commands: self.session_allowed_bash_commands,
            session_denied_bash_commands: self.session_denied_bash_commands,
            last_response_usage_tokens: self.last_response_usage_tokens,
            interrupt_loop_to_user_prompt: self.interrupt_loop_to_user_prompt,
            active_api_key_index: self.active_api_key_index,
            consecutive_429_errors: self.consecutive_429_errors,
            last_key_switch_time: self.last_key_switch_time,
            yolo_mode: self.yolo_mode,
        })
    }
}

impl Default for AppBuilder {
    fn default() -> Self {
        Self::new()
    }
}

impl App {
    pub fn current_model(&self) -> LlmProviderType {
        self.current_model
    }

    pub fn set_current_model(&mut self, new_model: LlmProviderType) {
        self.current_model = new_model;
        // Save to global state
        let mut global_state = load_global_state().unwrap_or(None).unwrap_or_else(|| {
            let new_state = GlobalAppState::new(new_model);
            session_management::global_state::save_global_state(&new_state);
            new_state
        });

        global_state.current_llm_provider = new_model;
        session_management::global_state::save_global_state(&global_state);

        // Reset the key index when changing models
        self.active_api_key_index = 0;

        self.llm = Arc::from(build_llm_provider(
            self.system_prompt.content.clone(),
            new_model.get_config(),
            &self.cli_args,
            self.active_api_key_index,
        ));
    }

    pub fn conversation_messages(&self) -> &Vec<ChatMessage> {
        &self.conversation_messages
    }

    pub fn set_conversation_messages(&mut self, messages: Vec<ChatMessage>) {
        self.conversation_messages = messages;
        if let Err(e) = save_session(self) {
            eprintln!("Error saving session: {e}");
        }
    }

    pub fn add_message(&mut self, message: ChatMessage) {
        self.conversation_messages.push(message);
        if let Err(e) = save_session(self) {
            eprintln!("Error saving session: {e}");
        }
    }

    pub fn last_response_usage_tokens(&self) -> Option<usize> {
        self.last_response_usage_tokens
    }

    pub fn set_last_response_usage_tokens(&mut self, tokens: Option<usize>) {
        self.last_response_usage_tokens = tokens;
        if let Err(e) = save_session(self) {
            eprintln!("Error saving session: {e}");
        }
    }

    pub fn code_was_changed(&self) -> bool {
        self.code_was_changed
    }

    pub fn mark_code_as_changed(&mut self) {
        self.code_was_changed = true;
    }

    pub fn reset_code_changed_flag(&mut self) {
        self.code_was_changed = false;
    }

    pub fn interrupt_loop_to_user_prompt(&self) -> bool {
        self.interrupt_loop_to_user_prompt
    }

    pub fn set_interrupt_loop_to_user_prompt(&mut self, aborted: bool) {
        self.interrupt_loop_to_user_prompt = aborted;
    }
}
