use crate::app::SystemPrompt;
use crate::cli::cli_args::{Cli, EditTool};
use crate::display::print::print_error;
use crate::workspace::workspace_for_system_prompt::create_workspace_for_system_prompt;
use std::fs;
use std::path::PathBuf;

/// Creates the system prompt, optionally including workspace structure
pub fn create_system_prompt(
    workspace_path: PathBuf,
    cli_args: &Cli,
    ignore_patterns: Option<Vec<String>>,
) -> SystemPrompt {
    if cli_args.no_system_prompt {
        return SystemPrompt {
            content: "".to_string(),
            with_custom_user_prompt: false,
            workspace_content: "".to_string(),
        };
    }
    let mut system_prompt_content = if cli_args.edit_tool == EditTool::OnlyWriteFile {
        include_str!("../../prompts/write_file_only_prompt.txt").to_string()
    } else {
        include_str!("../../prompts/system_prompt.txt").to_string()
        // include_str!("../../prompts/gemini-cli-trimmed.md").to_string()
    };
    let mut workspace_content_cleaned = String::new();

    // First, append custom system prompt if it exists (before workspace section)
    let mut with_custom_user_prompt = false;
    let dima_agent_path = PathBuf::from("dima-agent.md");
    if dima_agent_path.exists() {
        match fs::read_to_string(&dima_agent_path) {
            Ok(custom_prompt_content) => {
                let trimmed = custom_prompt_content.trim();
                if !trimmed.is_empty() {
                    with_custom_user_prompt = true;
                    system_prompt_content.push_str("\n\n");
                    system_prompt_content.push_str("Additional instructions:\n");
                    system_prompt_content.push_str(trimmed);
                }
            }
            Err(e) => {
                print_error(format!("Warning: Failed to read dima-agent.md: {e}"));
            }
        }
    }

    // Then, append workspace structure
    match create_workspace_for_system_prompt(
        workspace_path,
        cli_args.workspace_token_limit,
        ignore_patterns,
    ) {
        Ok(workspace_content) => {
            // Strip the <workspace> and </workspace> wrapper tags
            workspace_content_cleaned = workspace_content
                .strip_prefix("Here is the structure of the current workspace:\n<workspace>\n")
                .unwrap_or(&workspace_content)
                .strip_suffix("\n</workspace>")
                .unwrap_or(&workspace_content)
                .to_string();

            system_prompt_content.push_str("\n\n");
            system_prompt_content.push_str(&workspace_content);
        }
        Err(e) => {
            print_error(format!(
                "Warning: Failed to inject workspace structure: {e}"
            ));
        }
    }
    SystemPrompt {
        content: system_prompt_content,
        with_custom_user_prompt,
        workspace_content: workspace_content_cleaned,
    }
}
