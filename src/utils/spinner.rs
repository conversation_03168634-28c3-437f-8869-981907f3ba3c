use crate::display::print::print_error;
use console::truncate_str;
use crossterm::style::Stylize;
use crossterm::terminal::size;
use std::io::Write;
use std::sync::atomic::{AtomicBool, Ordering};
use std::sync::Arc;
use tokio::time::{interval, Duration};

pub struct Spinner {
    spinner_active: Arc<AtomicBool>,
    handle: Option<tokio::task::<PERSON><PERSON><PERSON><PERSON><PERSON><()>>,
}

const SPINNER_CHARS: [&str; 10] = ["⠋", "⠙", "⠹", "⠸", "⠼", "⠴", "⠦", "⠧", "⠇", "⠏"];

/// Truncate spinner text to fit within terminal width with safety margin
fn truncate_spinner_text(text: &str, terminal_width: u16) -> String {
    if terminal_width == 0 {
        return "...".to_string();
    }

    // Account for spinner character (1) + space (1) + safety margin (3)
    let usable_width = terminal_width.saturating_sub(5) as usize;
    if usable_width == 0 {
        return "...".to_string();
    }

    // Truncate the text to fit within the usable width
    truncate_str(text, usable_width, "…").to_string()
}

impl Spinner {
    pub fn new(text: &str) -> Self {
        let text = text.to_string();
        let spinner_active = Arc::new(AtomicBool::new(true));
        let spinner_active_clone = spinner_active.clone();

        let handle = tokio::spawn(async move {
            let mut index = 0;
            let mut interval = interval(Duration::from_millis(100));
            let (terminal_width, _) = size().unwrap_or((80, 24));
            let truncated_text = truncate_spinner_text(&text, terminal_width);

            while spinner_active_clone.load(Ordering::Relaxed) {
                print!(
                    "\x1b[2K\r{} {}",
                    SPINNER_CHARS[index].blue(),
                    truncated_text
                );

                std::io::stdout().flush().unwrap();
                index = (index + 1) % SPINNER_CHARS.len();
                interval.tick().await;
            }

            // Clear the spinner line
            print!("\x1b[2K\r");
            std::io::stdout().flush().unwrap();
        });

        Spinner {
            spinner_active,
            handle: Some(handle),
        }
    }

    pub fn hide(&self) {
        print!("\x1b[2K\r");
        std::io::stdout().flush().unwrap();
    }

    pub async fn stop(&mut self) {
        self.spinner_active.store(false, Ordering::Relaxed);
        if let Some(handle) = self.handle.take() {
            match handle.await {
                Ok(_) => {}
                Err(e) => {
                    if !e.is_cancelled() {
                        print_error(format!("Error stopping spinner: {e}"));
                    }
                }
            }
        }
    }
}

impl Drop for Spinner {
    fn drop(&mut self) {
        // This is called when the spinner is dropped without being explicitly stopped,
        // which happens on cancellation. The task is still running, so we abort it.
        if let Some(handle) = self.handle.take() {
            handle.abort();
            // And we clean up the line ourselves, synchronously.
            print!("\x1b[2K\r");
            std::io::stdout().flush().unwrap();
        }
    }
}
