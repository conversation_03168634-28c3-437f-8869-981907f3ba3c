use std::env;
use std::path::PathBuf;

/// Finds the git root directory by walking up the directory tree.
/// If not found, the current working directory is returned.
///
/// However, if the current directory is the home directory and no git root is found,
/// this will warn the user and ask for confirmation before proceeding.
pub fn find_git_root_or_cwd() -> PathBuf {
    find_git_root_or_cwd_with_force_cwd(false)
}

/// Finds the git root directory by walking up the directory tree.
/// If `force_cwd` is true, returns the current working directory without searching.
/// If not found and `force_cwd` is false, the current working directory is returned.
///
/// However, if the current directory is the home directory and no git root is found,
/// this will warn the user and ask for confirmation before proceeding.
pub fn find_git_root_or_cwd_with_force_cwd(force_cwd: bool) -> PathBuf {
    let current_dir = env::current_dir().expect("Failed to get current directory");

    if force_cwd {
        return current_dir;
    }
    let mut path = current_dir.as_path();

    // Check if we're starting from the home directory
    let is_home_dir = if let Some(home_dir) = dirs::home_dir() {
        current_dir == home_dir
    } else {
        false
    };

    loop {
        let git_dir = path.join(".git");
        if git_dir.exists() {
            return path.to_path_buf();
        }
        match path.parent() {
            Some(parent) => path = parent,
            None => {
                // No git root found, falling back to current directory
                let fallback_dir = env::current_dir().expect("Failed to get current directory");

                // If we're in the home directory and no git root was found, warn the user
                if is_home_dir {
                    eprintln!("WARNING: You're running this tool from your home directory without a git repository.");
                    eprintln!();
                    eprintln!("Recommended alternatives:");
                    eprintln!(
                        "  1. Navigate to a specific project directory: cd /path/to/your/project"
                    );
                    eprintln!("  2. Initialize a git repository: git init");
                    eprintln!();
                }

                return fallback_dir;
            }
        }
    }
}
