use crate::bash::command_executor::{execute_shell_command, CommandOutput};
use crate::display::print::{print_error, print_tool_request};
use crate::tools::utils::bash_permission::{check_bash_permission, PermissionStatus};
use crate::tools::utils::tool_result_truncation::truncate_tool_output_if_needed;
use crate::utils::spinner::Spinner;
use crate::{app::App, config::constants::BASH_COMMANDS_TIMEOUT_SECONDS};
use std::path::PathBuf;
use std::sync::{Arc, Mutex};
use std::time::Instant;

async fn run_bash_command(command: &str, cwd: PathBuf, spinner: &mut Spinner) -> String {
    let start_time = Instant::now();
    print_tool_request(format!("Running: {command}"));
    let command_output =
        execute_shell_command(command, cwd, BASH_COMMANDS_TIMEOUT_SECONDS, spinner, false).await;
    spinner.stop().await;
    let duration = start_time.elapsed().as_secs_f32();
    let formatted_duration = if duration.fract() == 0.0 {
        format!("{}s", duration as u64)
    } else {
        format!("{duration:.1}s")
    };

    match command_output {
        CommandOutput::Success { output } => {
            print_tool_request(format!("Finished in {formatted_duration}: {command}\n"));
            truncate_tool_output_if_needed(output.trim_end())
        }
        CommandOutput::Error { output, exit_code } => {
            let exit_code_str =
                exit_code.map_or("".to_string(), |code| format!(" with exit code {code}"));
            print_tool_request(format!(
                "Failed in {formatted_duration}{exit_code_str}: {command}\n"
            ));
            truncate_tool_output_if_needed(output.trim_end())
        }
        CommandOutput::Timeout {
            output,
            timeout_seconds,
        } => {
            let message = format!("Command '{command}' timed out after {timeout_seconds} seconds");
            print_tool_request(format!("{message}\n"));
            truncate_tool_output_if_needed(output.trim_end())
        }
        CommandOutput::SpawnError { message } => {
            print_error(message.clone());
            message
        }
    }
}

pub async fn execute_bash_tool(command: &str, cwd: PathBuf, app_arc: Arc<Mutex<App>>) -> String {
    let yolo_mode = {
        let app = app_arc.lock().unwrap();
        app.cli_args.yolo
    }; // `app` (MutexGuard) is dropped here

    if yolo_mode {
        let mut spinner = Spinner::new(format!("Running {command}...").as_str());
        return run_bash_command(command, cwd, &mut spinner).await;
    }

    let app_arc_clone = app_arc.clone();
    let command_clone = command.to_string();
    let permission_status = tokio::task::spawn_blocking(move || {
        let mut app = app_arc_clone.lock().unwrap(); // Lock the mutex to get mutable reference
        check_bash_permission(&mut app, &command_clone)
    })
    .await
    .unwrap_or_else(|e| {
        print_error(format!("Permission check task failed: {e}"));
        PermissionStatus::Denied // Return Denied on task failure
    });

    if permission_status == PermissionStatus::Cancelled {
        let app_arc_clone = app_arc.clone();
        let mut app = app_arc_clone.lock().unwrap();
        app.set_interrupt_loop_to_user_prompt(true);
        return "Command execution cancelled by user.".to_string();
    }

    if permission_status == PermissionStatus::Denied {
        return format!("Command execution denied by user: {command}");
    }

    let mut spinner = Spinner::new(format!("Running {command}...").as_str());
    run_bash_command(command, cwd, &mut spinner).await
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::tokens::token_estimator::estimate_tokens;
    use crate::tools::utils::tool_result_truncation::{MAX_TOKENS, TRUNCATE_TOKENS};

    #[tokio::test]
    async fn test_execute_shell_command() {
        // Test a normal command
        let mut spinner = Spinner::new("Test Spinner");
        let output_result = execute_shell_command(
            "echo 'hello world'",
            PathBuf::from("/"),
            10,
            &spinner,
            false,
        )
        .await;
        spinner.stop().await;

        let output_str = match output_result {
            CommandOutput::Success { output } => output,
            _ => panic!("Expected success for normal command"),
        };
        println!("Normal command output:\n{output_str}");

        // Test a command that generates 1 MB of text, using run_bash_command to apply truncation logic
        let mut spinner = Spinner::new("Test Spinner");
        let large_output_str = run_bash_command(
            "dd if=/dev/zero bs=1024 count=1024 | tr '\\0' 'y'",
            PathBuf::from("/"),
            &mut spinner,
        )
        .await;
        // Print only a concise summary to avoid massive stdout spam in tests
        let head = &large_output_str[..large_output_str
            .char_indices()
            .nth(80)
            .map(|(i, _)| i)
            .unwrap_or_else(|| large_output_str.len())];
        println!(
            "Large output len: {}\nhead(80 chars): {}",
            large_output_str.len(),
            head
        );

        // Verify that the output was truncated (if necessary)
        assert!(estimate_tokens(&large_output_str) <= MAX_TOKENS + TRUNCATE_TOKENS * 2);
    }
}
