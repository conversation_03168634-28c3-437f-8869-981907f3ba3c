use crate::display::print::print_tool_request;
use std::fs;
use std::path::PathBuf;

/// Returns the tool output for the open_files tool for the LLM
pub async fn execute_open_files_tool(file_paths: Vec<&str>, cwd: PathBuf) -> String {
    let mut llm_result: String = "".to_string();
    let mut display_result: String = "".to_string();
    let mut all_success = true;
    for file_path in file_paths.clone() {
        let file_path_buf = cwd.join(file_path);
        if PathBuf::from(&file_path_buf).exists() {
            let read_result = fs::read_to_string(&file_path_buf);
            match read_result {
                Ok(result) => {
                    let result = append_line_numbers(&result);
                    display_result.push_str(&format!("  - {file_path}\n"));
                    llm_result
                        .push_str(&format!("Successfully opened {file_path}:\n\n{result}\n\n"));
                }
                Err(e) => {
                    all_success = false;
                    display_result.push_str(&format!("  ❌ {file_path} (error: {e})\n"));
                    llm_result.push_str(&format!("Failed to open {file_path}: {e}\n\n"));
                }
            }
        } else {
            all_success = false;
            display_result.push_str(&format!("  ❌ {file_path} (not found)\n"));
            llm_result.push_str(&format!("{file_path} not found.\n\n"));
        }
    }
    if all_success && file_paths.len() == 1 {
        let file_path = file_paths[0];
        let _app = crate::app_setup::setup_app; // placeholder to avoid unused warnings if refactoring; no-op
                                                // Inline token display handled by caller now; this line just prints the base message
        print_tool_request(format!("Read file: {file_path}\n"));
    } else {
        print_tool_request(format!("Read files:\n{display_result}\n"));
    }
    llm_result.trim().to_string()
}

/// Starts at 0 for consistency with expand_code_chunks tool.
fn append_line_numbers(file_content: &str) -> String {
    file_content.into()
    /*    let lines: Vec<&str> = file_content.lines().collect();
        let total_lines = lines.len();
        let number_of_digits_in_last_line_number = total_lines.to_string().len();
        let mut result = String::new();
        for (i, line) in lines.into_iter().enumerate() {
            let line_number = i;
            result.push_str(&format!(
                "{line_number:number_of_digits_in_last_line_number$}: {line}\n"
            ));
        }
        result
    */
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_append_line_numbers() {
        let file_content = "hello\nworld\n".repeat(100);
        let result = append_line_numbers(&file_content);
        println!("{result}");
    }
}
