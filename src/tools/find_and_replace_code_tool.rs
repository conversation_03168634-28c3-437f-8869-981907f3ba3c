use crate::display::print::{print_error, print_tool_request};
use std::fs;
use std::path::PathBuf;
use std::process::Command;

// A basic implementation of Levenshtein distance.
fn levenshtein(a: &str, b: &str) -> usize {
    let a_chars: Vec<char> = a.chars().collect();
    let b_chars: Vec<char> = b.chars().collect();
    let m = a_chars.len();
    let n = b_chars.len();

    if m == 0 {
        return n;
    }
    if n == 0 {
        return m;
    }

    let mut dp = vec![vec![0; n + 1]; m + 1];

    for (i, row) in dp.iter_mut().enumerate().take(m + 1) {
        row[0] = i;
    }
    for j in 0..=n {
        dp[0][j] = j;
    }

    for i in 1..=m {
        for j in 1..=n {
            let cost = if a_chars[i - 1] == b_chars[j - 1] {
                0
            } else {
                1
            };
            dp[i][j] = (dp[i - 1][j] + 1)
                .min(dp[i][j - 1] + 1)
                .min(dp[i - 1][j - 1] + cost);
        }
    }

    dp[m][n]
}

pub async fn execute_find_and_replace_code_tool(
    file_path: &str,
    find: &str,
    replace: &str,
    cwd: PathBuf,
) -> Result<String, String> {
    let full_path = cwd.join(file_path);
    if !full_path.exists() {
        let error_msg = format!("File not found: {file_path}");
        print_error(error_msg.clone());
        return Err(error_msg);
    }
    if find == replace {
        let error_msg = format!("Find and replace strings are the same:\n{find}");
        print_error(error_msg.clone());
        return Err(error_msg);
    }

    let content = match fs::read_to_string(&full_path) {
        Ok(c) => c,
        Err(e) => {
            let error_msg = format!("Failed to read file {file_path}: {e}");
            print_error(error_msg.clone());
            return Err(error_msg);
        }
    };

    let occurrences = content.matches(find).count();
    if occurrences > 1 {
        let error_msg = "Multiple matches found. Use a more specific 'find' string.".to_string();
        print_error(error_msg.clone());
        return Err(error_msg);
    }

    if occurrences == 0 {
        let find_lines: Vec<&str> = find.lines().collect();
        let content_lines: Vec<&str> = content.lines().collect();

        if find_lines.is_empty() {
            return Err("Find string is empty.".to_string());
        }

        if content_lines.is_empty() || content_lines.len() < find_lines.len() {
            let err_msg = format!(
                "{file_path}: No matches found for find string:\n```\n{find}\n```\nThe file is shorter than the find string or is empty."
            );
            print_error(err_msg.clone());
            return Err(err_msg);
        }

        let mut min_distance = usize::MAX;
        let mut best_match_window: &[&str] = &[];

        for window in content_lines.windows(find_lines.len()) {
            let window_text = window.join("\n");
            let distance = levenshtein(find, &window_text);
            if distance < min_distance {
                min_distance = distance;
                best_match_window = window;
            }
        }

        let closest_match_str = best_match_window.join("\n");

        let temp_dir = std::env::temp_dir();
        let find_file_path = temp_dir.join("dima_find.tmp");
        let match_file_path = temp_dir.join("dima_match.tmp");

        fs::write(&find_file_path, find).map_err(|e| format!("Failed to write temp file: {e}"))?;
        fs::write(&match_file_path, &closest_match_str)
            .map_err(|e| format!("Failed to write temp file: {e}"))?;

        let output = Command::new("git")
            .arg("diff")
            .arg("--no-index")
            .arg("--color=never")
            .arg(&match_file_path)
            .arg(&find_file_path)
            .output()
            .map_err(|e| e.to_string())?;
        let _ = fs::remove_file(&find_file_path);
        let _ = fs::remove_file(&match_file_path);

        let diff = String::from_utf8_lossy(&output.stdout)
            .lines()
            .skip(4)
            .collect::<Vec<_>>()
            .join("\n");

        let error_msg = format!("{file_path}: No matches found for find string:\n```\n{find}\n```\nThe diff with the closest match is:\n```\n{diff}\n```\n\nThe code section with the closest match has been expanded in the file for review.");
        let full_error = error_msg.to_string();
        let trimmed = find.trim();
        // Avoid slicing in the middle of a UTF-8 character. Take 60 Unicode scalar values.
        let truncated_start = {
            let mut end = 0usize;
            let mut count = 0usize;
            for (idx, _) in trimmed.char_indices() {
                if count == 60 {
                    break;
                }
                end = idx;
                count += 1;
            }
            // If there were fewer than 60 chars, end stays at last char boundary via len()
            if count < 60 {
                trimmed.to_string()
            } else {
                format!("{}...", &trimmed[..end])
            }
        };
        print_error(format!(
            "Failed to edit {file_path}. No matches found for:\n{truncated_start}"
        ));
        return Err(full_error);
    }

    let new_content = content.replacen(find, replace, 1);
    let temp_file_name = format!("{}.tmp", file_path.replace(['/', '\\'], "_"));
    let temp_file_path = cwd.join(&temp_file_name);
    if let Err(e) = fs::write(&temp_file_path, &new_content) {
        let error_msg = format!("Failed to write to temporary file: {e}");
        print_error(error_msg.clone());
        let _ = fs::remove_file(&temp_file_path);
        return Err(error_msg);
    };

    // Run git diff from the workspace root with relative paths to get clean output
    let run_diff = |color_option: &str| {
        Command::new("git")
            .current_dir(&cwd)
            .arg("diff")
            .arg("--no-index")
            .arg(format!("--color={color_option}"))
            .arg(file_path)
            .arg(&temp_file_name)
            .output()
    };

    let colored_output_res = run_diff("always");
    let uncolored_output_res = run_diff("never");

    let _ = fs::remove_file(&temp_file_path);

    match (colored_output_res, uncolored_output_res) {
        (Ok(colored_output), Ok(uncolored_output)) => {
            let colored_diff = crate::utils::diff_utils::clean_diff(
                &colored_output.stdout,
                &temp_file_name,
                file_path,
            );
            let uncolored_diff = crate::utils::diff_utils::clean_diff(
                &uncolored_output.stdout,
                &temp_file_name,
                file_path,
            );

            if let Err(e) = fs::write(&full_path, &new_content) {
                let error_msg = format!("Failed to write to file {file_path}: {e}");
                print_error(error_msg.clone());
                return Err(error_msg);
            }
            let success_message = format!("Successfully replaced code in {file_path}\n");
            print_tool_request(success_message.clone());
            println!("{colored_diff}");
            Ok(format!("{success_message}\n\n{uncolored_diff}"))
        }
        (Err(e), _) | (_, Err(e)) => {
            let error_msg = format!("Failed to generate diff: {e}");
            print_error(error_msg.clone());
            Err(error_msg)
        }
    }
}
