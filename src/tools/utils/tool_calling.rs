use crate::app::App;
use crate::tools::bash_tool::execute_bash_tool;
use crate::tools::delete_file_tool::execute_delete_file_tool;
use crate::tools::find_and_replace_code_tool::execute_find_and_replace_code_tool;
use crate::tools::grep_file_content_tool::execute_grep_file_content_tool;
use crate::tools::open_files_tool::execute_open_files_tool;
use crate::tools::utils::llm_provider_tool_setup::tool_names_as_string;
use crate::tools::utils::USER_CANCELLED_TOOL_EXECUTION;
use crate::tools::write_file_tool::execute_write_file_tool;
use llm::ToolCall;
use serde_json::Value;
use std::sync::{Arc, Mutex};

pub async fn process_tool_call(
    app_arc: Arc<Mutex<App>>,
    tool_call: &ToolCall,
) -> Result<String, String> {
    let is_code_change_tool = matches!(
        tool_call.function.name.as_str(),
        "write_file" | "delete_file" | "find_and_replace_code"
    );

    let result = execute_tool_call(app_arc.clone(), tool_call).await;

    if is_code_change_tool && result.is_ok() {
        app_arc.lock().unwrap().mark_code_as_changed();
    }

    result
}

async fn execute_tool_call(
    app_arc: Arc<Mutex<App>>,
    tool_call: &ToolCall,
) -> Result<String, String> {
    // Optional: estimate token sizes for tool requests and responses
    use crate::cli::cli_args::ToolTokenDisplay;
    use crate::display::print::print_tool_request;
    use crate::tokens::token_estimator::{estimate_tokens, estimate_tokens_format_nicely};

    // Helper for token printing
    fn print_tool_tokens(
        display_mode: ToolTokenDisplay,
        request: &str,
        args: &str,
        result: Result<&str, &str>,
    ) {
        if matches!(display_mode, ToolTokenDisplay::Off) {
            return;
        }
        match result {
            Ok(res) => {
                if matches!(display_mode, ToolTokenDisplay::Breakdown) {
                    print_tool_request(format!(
                        "[tokens req~{}, args~{}, res~{}]\n",
                        estimate_tokens_format_nicely(request),
                        estimate_tokens_format_nicely(args),
                        estimate_tokens_format_nicely(res)
                    ));
                } else {
                    let total =
                        estimate_tokens(request) + estimate_tokens(args) + estimate_tokens(res);
                    print_tool_request(format!(
                        "[tokens ~{}]\n",
                        estimate_tokens_format_nicely(&" ".repeat(total))
                    ));
                }
            }
            Err(err) => {
                if matches!(display_mode, ToolTokenDisplay::Breakdown) {
                    print_tool_request(format!(
                        "[tokens req~{}, args~{}, err~{}]\n",
                        estimate_tokens_format_nicely(request),
                        estimate_tokens_format_nicely(args),
                        estimate_tokens_format_nicely(err)
                    ));
                } else {
                    let total =
                        estimate_tokens(request) + estimate_tokens(args) + estimate_tokens(err);
                    print_tool_request(format!(
                        "[tokens ~{}]\n",
                        estimate_tokens_format_nicely(&" ".repeat(total))
                    ));
                }
            }
        }
    }

    let display_mode = app_arc.lock().unwrap().cli_args.show_tool_tokens; // Off | Total | Breakdown

    match tool_call.function.name.as_str() {
        "open_files" => {
            let args = parse_arguments(tool_call)?;
            let file_paths_as_json_value = args
                .get("file_paths")
                .ok_or("Missing required 'file_paths' argument".to_string())?
                .as_array()
                .ok_or("file_paths must be an array".to_string())?;
            let file_paths: Result<Vec<&str>, String> = file_paths_as_json_value
                .iter()
                .map(|file_path| {
                    file_path
                        .as_str()
                        .ok_or("file_paths must be an array of strings".to_string())
                })
                .collect();
            let file_paths = file_paths?;
            let cwd = app_arc.lock().unwrap().working_dir.clone();
            let request = format!("open_files: {file_paths:?}");
            let result = execute_open_files_tool(file_paths, cwd).await;
            print_tool_tokens(
                display_mode,
                &request,
                &tool_call.function.arguments,
                Ok(&result),
            );
            Ok(result)
        }
        "grep_file_content" => {
            let args = parse_arguments(tool_call)?;
            let pattern = args
                .get("pattern")
                .ok_or("Missing required 'pattern' argument".to_string())?
                .as_str()
                .ok_or("pattern must be a string".to_string())?;
            let cwd = app_arc.lock().unwrap().working_dir.clone();
            let request = format!("grep_file_content: {pattern}");
            let out = execute_grep_file_content_tool(pattern, cwd)
                .await
                .map_err(|e| format!("Failed to execute grep_file_content tool: {e}"));
            match &out {
                Ok(res) => print_tool_tokens(
                    display_mode,
                    &request,
                    &tool_call.function.arguments,
                    Ok(res),
                ),
                Err(err) => print_tool_tokens(
                    display_mode,
                    &request,
                    &tool_call.function.arguments,
                    Err(err),
                ),
            }
            out
        }
        "bash" => {
            let args = parse_arguments(tool_call)?;
            let command = args
                .get("command")
                .ok_or("Missing required 'command' argument".to_string())?
                .as_str()
                .ok_or("command must be a string".to_string())?;
            let cwd = app_arc.lock().unwrap().working_dir.clone();
            let request = format!("bash: {command}");
            let result = execute_bash_tool(command, cwd, app_arc.clone()).await;
            print_tool_tokens(
                display_mode,
                &request,
                &tool_call.function.arguments,
                Ok(&result),
            );
            if result == USER_CANCELLED_TOOL_EXECUTION {
                return Err(USER_CANCELLED_TOOL_EXECUTION.to_string());
            }
            Ok(result)
        }
        "write_file" => {
            let args = parse_arguments(tool_call)?;
            let file_path = args
                .get("file_path")
                .ok_or("Missing required 'file_path' argument".to_string())?
                .as_str()
                .ok_or("file_path must be a string".to_string())?;
            let content = args.get("content").and_then(|v| v.as_str());

            let overwrite = {
                let app = app_arc.lock().unwrap();
                let disable_overwrite_protection = app.cli_args.use_override_in_write_file_tool;
                if disable_overwrite_protection {
                    Some(true)
                } else {
                    args.get("overwrite").and_then(|v| v.as_bool())
                }
            };
            let cwd = app_arc.lock().unwrap().working_dir.clone();
            let request = format!(
                "write_file: path={file_path}, overwrite={:?}, content_len={}",
                overwrite,
                content.unwrap_or("").len()
            );
            let out = execute_write_file_tool(file_path, content, overwrite, cwd).await;
            match &out {
                Ok(res) => print_tool_tokens(
                    display_mode,
                    &request,
                    &tool_call.function.arguments,
                    Ok(res),
                ),
                Err(err) => print_tool_tokens(
                    display_mode,
                    &request,
                    &tool_call.function.arguments,
                    Err(err),
                ),
            }
            out
        }
        "delete_file" => {
            let args = parse_arguments(tool_call)?;
            let file_path = args
                .get("file_path")
                .ok_or("Missing required 'file_path' argument".to_string())?
                .as_str()
                .ok_or("file_path must be a string".to_string())?;
            let cwd = app_arc.lock().unwrap().working_dir.clone();
            let request = format!("delete_file: path={file_path}");
            let out = execute_delete_file_tool(file_path, cwd).await;
            match &out {
                Ok(res) => print_tool_tokens(
                    display_mode,
                    &request,
                    &tool_call.function.arguments,
                    Ok(res),
                ),
                Err(err) => print_tool_tokens(
                    display_mode,
                    &request,
                    &tool_call.function.arguments,
                    Err(err),
                ),
            }
            out
        }
        "find_and_replace_code" => {
            let args = parse_arguments(tool_call)?;
            let file_path = args
                .get("file_path")
                .ok_or("Missing required 'file_path' argument".to_string())?
                .as_str()
                .ok_or("file_path must be a string".to_string())?;
            let find = args
                .get("find")
                .ok_or("Missing required 'find' argument".to_string())?
                .as_str()
                .ok_or("find must be a string".to_string())?;
            let replace = args
                .get("replace")
                .ok_or("Missing required 'replace' argument".to_string())?
                .as_str()
                .ok_or("replace must be a string".to_string())?;
            let cwd = app_arc.lock().unwrap().working_dir.clone();
            let request = format!(
                "find_and_replace_code: path={file_path}, find_len={}, replace_len={}",
                find.len(),
                replace.len()
            );
            let out = execute_find_and_replace_code_tool(file_path, find, replace, cwd).await;
            match &out {
                Ok(res) => print_tool_tokens(
                    display_mode,
                    &request,
                    &tool_call.function.arguments,
                    Ok(res),
                ),
                Err(err) => print_tool_tokens(
                    display_mode,
                    &request,
                    &tool_call.function.arguments,
                    Err(err),
                ),
            }
            out
        }
        _ => {
            let cli_args = app_arc.lock().unwrap().cli_args.clone();
            Err(format!(
                "Unknown function: {}. Known functions: {}",
                tool_call.function.name,
                tool_names_as_string(&cli_args)
            ))
        }
    }
}

fn parse_arguments(tool_call: &ToolCall) -> Result<Value, String> {
    serde_json::from_str::<Value>(&tool_call.function.arguments).map_err(|e| {
        format!(
            "Failed to parse tool call arguments: {}. Input: '{}'",
            e, tool_call.function.arguments
        )
    })
}
