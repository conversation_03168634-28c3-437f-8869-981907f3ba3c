use crate::app::{<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, System<PERSON>rom<PERSON>};
use crate::cli::cli_args::Cli;
use crate::config::user_agent_config::load_dima_agent_config;
use crate::display::print::{print_command, print_error};
use crate::provider_models::llm_provider_builder;
use crate::provider_models::llm_provider_type::LlmProviderType;
use crate::session_management::global_state::{self, load_global_state, GlobalAppState};
use crate::session_management::session_manager::{self, Session};
use crate::utils::git::find_git_root_or_cwd_with_force_cwd;
use crate::utils::system_prompt::create_system_prompt;
use crossterm::{execute, terminal::SetTitle};
use session_manager::{find_latest_session_for_working_dir, generate_session_id, load_session};
use std::error::Error;
use std::sync::{Arc, Mutex};

/// Encapsulates information about a restored session.
#[derive(Clone)]
pub struct RestoreInfo {
    pub session_id: String,
    pub message_count: usize,
}

/// Encapsulates the application setup logic.
/// This function parses CLI arguments, loads configuration, sets up the LLM,
/// and restores a session if requested.
pub async fn setup_app(
    cli_args: Cli,
) -> Result<(Arc<Mutex<App>>, SystemPrompt, Option<RestoreInfo>), Box<dyn Error>> {
    let cwd = find_git_root_or_cwd_with_force_cwd(cli_args.force_cwd);

    // Determine the current model by following this priority:
    // 1. CLI argument (`--llm-provider`)
    // 2. Model from the last session (`state.json`)
    // 3. Default model (`PollinationsAiGpt4_1`)
    let dima_config = load_dima_agent_config(&cwd);

    let global_state = load_global_state().unwrap_or(None);

    let current_model = match cli_args.llm_provider {
        Some(cli_model) => {
            // If CLI model is different from state, update state
            if global_state
                .as_ref()
                .is_none_or(|s| s.current_llm_provider != cli_model)
            {
                let mut state = global_state.unwrap_or_else(|| GlobalAppState::new(cli_model));
                state.current_llm_provider = cli_model;
                global_state::save_global_state(&state);
            }
            cli_model
        }
        None => match global_state {
            Some(state) => state.current_llm_provider,
            None => {
                // No CLI arg, no global state, use default and save it
                let default_model = LlmProviderType::default();
                let new_state = GlobalAppState::new(default_model);
                global_state::save_global_state(&new_state);
                default_model
            }
        },
    };

    // Create system prompt and LLM provider
    let system_prompt = create_system_prompt(
        cwd.clone(),
        &cli_args,
        dima_config
            .as_ref()
            .and_then(|c| c.ignore_workspace.clone()),
    );
    let llm = llm_provider_builder::build_llm_provider(
        system_prompt.clone().content,
        current_model.get_config(),
        &cli_args,
        0, // Initial key index is 0
    );

    // Handle session restoring logic
    let mut session_to_load: Option<Session> = None;
    let mut restore_info: Option<RestoreInfo> = None;

    let session_id = if let Some(restore_arg) = &cli_args.restore {
        if restore_arg.is_empty() {
            match find_latest_session_for_working_dir(&cwd) {
                Ok(Some(latest_id)) => match load_session(&latest_id) {
                    Ok(session) => {
                        let message_count = session.messages.len();
                        restore_info = Some(RestoreInfo {
                            session_id: latest_id.clone(),
                            message_count,
                        });
                        session_to_load = Some(session);
                        latest_id
                    }
                    Err(e) => {
                        print_error(format!("Failed to load session {latest_id}: {e}"));
                        generate_session_id(&cwd)
                    }
                },
                Ok(None) => {
                    print_command(
                        "No previous session found for this directory. Starting a new session.",
                    );
                    generate_session_id(&cwd)
                }
                Err(e) => {
                    print_error(format!("Error finding latest session: {e}"));
                    generate_session_id(&cwd)
                }
            }
        } else {
            print_command(format!("Restoring session: {restore_arg}"));
            match load_session(restore_arg) {
                Ok(session) => {
                    let message_count = session.messages.len();
                    restore_info = Some(RestoreInfo {
                        session_id: restore_arg.clone(),
                        message_count,
                    });
                    session_to_load = Some(session);
                    restore_arg.clone()
                }
                Err(e) => {
                    print_error(format!("Failed to load session {restore_arg}: {e}"));
                    generate_session_id(&cwd)
                }
            }
        }
    } else {
        generate_session_id(&cwd)
    };

    // Build the App instance using the builder
    let mut app = AppBuilder::new()
        .working_dir(cwd.clone())
        .cli_args(cli_args)
        .system_prompt(system_prompt.clone())
        .llm(llm)
        .current_model(current_model)
        .session_id(session_id)
        .dima_config(dima_config)
        .build()?;

    // If a session was loaded, apply its state to the App instance
    if let Some(loaded_session) = session_to_load {
        app.set_conversation_messages(loaded_session.messages);
        app.set_last_response_usage_tokens(loaded_session.metadata.last_response_usage_tokens);
        // These lines ensure that the bash commands are loaded from the session metadata
        app.session_allowed_bash_commands = loaded_session.metadata.allowed_bash_commands;
        app.session_denied_bash_commands = loaded_session.metadata.denied_bash_commands;
        app.yolo_mode = loaded_session.metadata.yolo_mode;
    }

    // Set terminal title
    let working_dir_name = cwd
        .file_name()
        .and_then(|name| name.to_str())
        .unwrap_or("unknown");
    let title = format!("Dima AI - {working_dir_name}");
    if let Err(e) = execute!(std::io::stdout(), SetTitle(title)) {
        eprintln!("Error setting terminal title: {e}");
    }

    Ok((Arc::new(Mutex::new(app)), system_prompt, restore_info))
}
