use crate::provider_models::llm_provider_type::LlmProviderType;
use crate::session_management::session_dir::get_session_dir;
use serde::{Deserialize, Serialize};
use std::fs;
use std::io;
use std::path::PathBuf;

const GLOBAL_STATE_FILE: &str = "state.json";

pub fn get_global_state_path() -> PathBuf {
    get_session_dir().join(GLOBAL_STATE_FILE)
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GlobalAppState {
    pub current_llm_provider: LlmProviderType,
    #[serde(default)]
    pub allowed_bash_commands: Vec<String>,
    #[serde(default)]
    pub denied_bash_commands: Vec<String>,
}

impl GlobalAppState {
    pub fn new(current_llm_provider: LlmProviderType) -> Self {
        Self {
            current_llm_provider,
            allowed_bash_commands: vec![],
            denied_bash_commands: vec![],
        }
    }
}

pub fn load_global_state() -> io::Result<Option<GlobalAppState>> {
    let path = get_global_state_path();
    if !path.exists() {
        return Ok(None);
    }

    let json = fs::read_to_string(path)?;
    let state: GlobalAppState = serde_json::from_str(&json)?;
    Ok(Some(state))
}

pub fn save_global_state(state: &GlobalAppState) {
    let path = get_global_state_path();
    fs::create_dir_all(path.parent().unwrap()).expect("Failed to create directory");
    let json = serde_json::to_string_pretty(state).expect("Failed to serialize global state");
    fs::write(path, json).expect("Failed to write global state");
}
