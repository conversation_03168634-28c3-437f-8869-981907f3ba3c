use crate::app::App;
use crate::display::print::{print_error, print_warning};
use crate::dot_commands::handler::command_handler::{CommandContext, DotCommand, DotCommandResult};
use llm::chat::ChatRole;
use std::future::Future;
use std::pin::Pin;
use std::sync::{Arc, Mutex};

pub fn handle_retry_command(app_arc: &Arc<Mutex<App>>) -> DotCommandResult {
    let app = app_arc.lock().unwrap();
    if let Some(last_message) = app.conversation_messages().last() {
        if last_message.role == ChatRole::User {
            DotCommandResult::Retry
        } else {
            let warning_message =
                "Cannot retry because the last message was from AI. Just type your message.";
            print_warning(warning_message);
            DotCommandResult::Continue
        }
    } else {
        print_error("No conversation history to retry".into());
        DotCommandResult::Continue
    }
}

pub struct RetryCommand;

impl DotCommand for RetryCommand {
    fn name(&self) -> &'static str {
        ".retry"
    }
    fn description(&self) -> &'static str {
        "Retry the last request"
    }
    fn execute<'a>(
        &'a self,
        ctx: CommandContext<'a>,
    ) -> Pin<Box<dyn Future<Output = DotCommandResult> + Send + 'a>> {
        Box::pin(async move { handle_retry_command(ctx.app_arc) })
    }
}
