use crate::app::App;
use crate::display::print::{print_command, print_error};
use crate::dot_commands::copilot_usage_command::handle_copilot_usage_command;
use crate::dot_commands::handler::command_handler::{CommandContext, DotCommand, DotCommandResult};
use std::future::Future;
use std::pin::Pin;
use std::sync::{Arc, Mutex};

pub fn handle_reauth_github_copilot_command(app_arc: &Arc<Mutex<App>>) {
    let app = app_arc.lock().unwrap();

    // Check if the current model is GitHub Copilot
    if !app.current_model().get_config().is_github_copilot {
        print_error("The current model is not GitHub Copilot. Relogin is only available for GitHub Copilot models.".to_string());
        return;
    }

    print_command("Initiating GitHub Copilot re-authentication...".to_string());

    // Call the relogin method on the LLM provider
    match app.llm.relogin_github_copilot() {
        Ok(()) => {
            print_command("GitHub Copilot re-authentication completed successfully!".to_string());
            // Drop the lock before calling handle_copilot_usage_command to avoid deadlock
            drop(app);
            // After successful reauth, show usage information
            handle_copilot_usage_command(app_arc);
        }
        Err(e) => {
            print_error(format!("GitHub Copilot re-authentication failed: {e}"));
        }
    }
}

pub struct ReauthCopilotCommand;

impl DotCommand for ReauthCopilotCommand {
    fn name(&self) -> &'static str {
        ".reauth-copilot"
    }
    fn description(&self) -> &'static str {
        "Re-authenticate with GitHub Copilot"
    }
    fn execute<'a>(
        &'a self,
        ctx: CommandContext<'a>,
    ) -> Pin<Box<dyn Future<Output = DotCommandResult> + Send + 'a>> {
        Box::pin(async move {
            handle_reauth_github_copilot_command(ctx.app_arc);
            DotCommandResult::Continue
        })
    }
}
