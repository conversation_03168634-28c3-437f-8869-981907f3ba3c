use crate::app::App;
use crate::display::print::print_command;
use crate::dot_commands::handler::command_handler::{Command<PERSON>ontext, DotCommand, DotCommandResult};
use crate::tokens::token_estimator::estimate_tokens;
use llm::chat::{ChatMessage, MessageType};
use std::future::Future;
use std::pin::Pin;
use std::sync::{Arc, Mutex};

const USER_MESSAGE_TOKEN_LIMIT: usize = 1000;
const AI_MESSAGE_TOKEN_LIMIT: usize = 200;
const PRUNE_TRUNCATE_CHAR_LIMIT: usize = 100;
const PRUNE_TRUNCATE_SUFFIX: &str = " [truncated for prune]";

fn truncate_content(content: &str, max_tokens: usize) -> String {
    let token_count = estimate_tokens(content);
    if token_count <= max_tokens {
        return content.to_string();
    }

    // Calculate how many characters to keep (roughly 4 chars per token)
    let max_chars = max_tokens * 4;
    let chars: Vec<char> = content.chars().collect();

    if chars.len() <= max_chars {
        return content.to_string();
    }

    // Take first portion and add truncation indicator
    let truncated: String = chars.iter().take(max_chars).collect();
    format!("{truncated}... [truncated from {token_count} to {max_tokens} estimated tokens]")
}

fn message_estimated_tokens(msg: &ChatMessage) -> usize {
    let mut total = estimate_tokens(&msg.content);
    match &msg.message_type {
        MessageType::ToolUse(calls) => {
            for call in calls {
                total += estimate_tokens(&call.function.arguments);
            }
        }
        MessageType::ToolResult(results) => {
            for call in results {
                total += estimate_tokens(&call.function.arguments);
            }
        }
        _ => {}
    }
    total
}

fn prune_messages(messages: Vec<ChatMessage>) -> Vec<ChatMessage> {
    messages
        .into_iter()
        .map(|mut message| {
            match &mut message.message_type {
                // For tool results, keep the message but truncate heavy payloads
                MessageType::ToolResult(results) => {
                    // Truncate top-level content if present
                    if message.content.chars().count() > PRUNE_TRUNCATE_CHAR_LIMIT {
                        let truncated: String = message
                            .content
                            .chars()
                            .take(PRUNE_TRUNCATE_CHAR_LIMIT)
                            .collect();
                        message.content = format!("{truncated}{PRUNE_TRUNCATE_SUFFIX}");
                    }
                    // Truncate each result's function.arguments
                    for call in results.iter_mut() {
                        if call.function.arguments.chars().count() > PRUNE_TRUNCATE_CHAR_LIMIT {
                            let truncated: String = call
                                .function
                                .arguments
                                .chars()
                                .take(PRUNE_TRUNCATE_CHAR_LIMIT)
                                .collect();
                            call.function.arguments = format!("{truncated}{PRUNE_TRUNCATE_SUFFIX}");
                        }
                    }
                    message
                }
                // For tool use, truncate each call's function.arguments
                MessageType::ToolUse(calls) => {
                    for call in calls.iter_mut() {
                        if call.function.arguments.chars().count() > PRUNE_TRUNCATE_CHAR_LIMIT {
                            let truncated: String = call
                                .function
                                .arguments
                                .chars()
                                .take(PRUNE_TRUNCATE_CHAR_LIMIT)
                                .collect();
                            call.function.arguments = format!("{truncated}{PRUNE_TRUNCATE_SUFFIX}");
                        }
                    }
                    message
                }
                // Truncate user messages to 1k tokens
                _ if matches!(message.role, llm::chat::ChatRole::User) => {
                    message.content = truncate_content(&message.content, USER_MESSAGE_TOKEN_LIMIT);
                    message
                }
                // Truncate AI messages to 200 tokens
                _ if matches!(message.role, llm::chat::ChatRole::Assistant) => {
                    message.content = truncate_content(&message.content, AI_MESSAGE_TOKEN_LIMIT);
                    message
                }
                _ => message,
            }
        })
        .collect()
}

pub fn handle_prune_command(app_arc: &Arc<Mutex<App>>) {
    let mut app = app_arc.lock().unwrap();
    let original_messages = app.conversation_messages().clone();

    // Check if conversation is empty
    if original_messages.is_empty() {
        print_command("No messages to prune - conversation is empty.");
        return;
    }

    // Calculate original token counts
    let original_total_tokens: usize = original_messages.iter().map(message_estimated_tokens).sum();

    // Count tool result presence and tokens more accurately
    let mut original_tool_result_count: usize = 0;
    let mut original_tool_result_tokens: usize = 0;
    for msg in &original_messages {
        if let MessageType::ToolResult(results) = &msg.message_type {
            original_tool_result_count += 1;
            // Count both message content and the JSON payload size for better estimation
            original_tool_result_tokens += estimate_tokens(&msg.content);
            for call in results {
                original_tool_result_tokens += estimate_tokens(&call.function.arguments);
            }
        }
    }

    // Count messages that would be truncated
    let user_messages_over_limit = original_messages
        .iter()
        .filter(|msg| {
            matches!(msg.role, llm::chat::ChatRole::User)
                && !matches!(msg.message_type, MessageType::ToolResult(_))
                && estimate_tokens(&msg.content) > USER_MESSAGE_TOKEN_LIMIT
        })
        .count();

    let ai_messages_over_limit = original_messages
        .iter()
        .filter(|msg| {
            matches!(msg.role, llm::chat::ChatRole::Assistant)
                && estimate_tokens(&msg.content) > AI_MESSAGE_TOKEN_LIMIT
        })
        .count();

    // Check if there's nothing to prune
    if original_tool_result_count == 0
        && user_messages_over_limit == 0
        && ai_messages_over_limit == 0
    {
        print_command(format!(
            "Nothing to prune - conversation is already optimized.\n\
             Current status:\n\
             • Total estimated tokens: {original_total_tokens}\n\
             • Tool results: 0 tokens\n\
             • User messages: all under {USER_MESSAGE_TOKEN_LIMIT} token limit\n\
             • AI messages: all under {AI_MESSAGE_TOKEN_LIMIT} token limit"
        ));
        return;
    }

    // Prune the messages
    let pruned_messages = prune_messages(original_messages);

    // Calculate new token counts
    let new_total_tokens: usize = pruned_messages.iter().map(message_estimated_tokens).sum();

    use num_format::{Locale, ToFormattedString};
    let original_tool_result_tokens_fmt =
        original_tool_result_tokens.to_formatted_string(&Locale::en);
    let user_messages_over_limit_fmt = user_messages_over_limit.to_string();
    let user_limit_fmt = USER_MESSAGE_TOKEN_LIMIT.to_formatted_string(&Locale::en);
    let ai_messages_over_limit_fmt = ai_messages_over_limit.to_string();
    let ai_limit_fmt = AI_MESSAGE_TOKEN_LIMIT.to_formatted_string(&Locale::en);
    let original_total_tokens_fmt = original_total_tokens.to_formatted_string(&Locale::en);
    let new_total_tokens_fmt = new_total_tokens.to_formatted_string(&Locale::en);
    let removed_total_tokens_fmt = original_total_tokens
        .saturating_sub(new_total_tokens)
        .to_formatted_string(&Locale::en);

    // Update the conversation
    app.set_conversation_messages(pruned_messages);

    print_command(format!(
        "Conversation pruned:\n\
         • Tool results: dropped entirely (~{original_tool_result_tokens_fmt} tokens removed)\n\
         • User messages: {user_messages_over_limit_fmt} truncated, capped at {user_limit_fmt} tokens each\n\
         • AI messages: {ai_messages_over_limit_fmt} truncated, capped at {ai_limit_fmt} tokens each\n\
         • Total tokens: ~{original_total_tokens_fmt} → ~{new_total_tokens_fmt} (~{removed_total_tokens_fmt} tokens removed)

         % context left will be updated on next AI response"
    ));
}

pub struct PruneCommand;

impl DotCommand for PruneCommand {
    fn name(&self) -> &'static str {
        ".prune"
    }

    fn description(&self) -> &'static str {
        "Prune conversation: drop tool results, cap user messages to 1k tokens, AI messages to 200 tokens"
    }

    fn execute<'a>(
        &'a self,
        ctx: CommandContext<'a>,
    ) -> Pin<Box<dyn Future<Output = DotCommandResult> + Send + 'a>> {
        Box::pin(async move {
            handle_prune_command(ctx.app_arc);
            DotCommandResult::Continue
        })
    }
}
