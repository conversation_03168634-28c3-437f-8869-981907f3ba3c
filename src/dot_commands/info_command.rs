use crate::app::App;
use crate::display::print::print_command;
use crate::dot_commands::handler::command_handler::{Command<PERSON>ontext, DotCommand, DotCommandResult};
use crate::tokens::token_estimator::estimate_tokens;
use llm::chat::ChatRole;
use std::future::Future;
use std::pin::Pin;
use std::sync::{Arc, Mutex};

pub fn handle_info_command(app_arc: &Arc<Mutex<App>>) {
    let app = app_arc.lock().unwrap();

    let model_name = app.current_model().get_config().startup_banner_text;
    let model_context_window = app.current_model().get_config().context_window_size;
    let working_dir = app.working_dir.display();
    let last_response_usage_tokens = app.last_response_usage_tokens().unwrap_or(0);
    let session_id = &app.session_id;

    let lint_command_info = format!(
        "Lint command: {}",
        app.dima_config
            .as_ref()
            .and_then(|c| c.lint_command.as_deref())
            .unwrap_or("not set")
    );

    let edit_tool_info = format!("Edit tool: {:?}", app.cli_args.edit_tool);

    let mut total_estimated_tokens = 0;
    let mut tool_estimated_tokens = 0;
    let mut user_message_count = 0;
    let mut assistant_message_count = 0;
    let mut tool_use_count = 0;
    let mut tool_result_count = 0;

    // Add system prompt tokens if conversation is initially empty or no messages
    if app.conversation_messages().is_empty() {
        total_estimated_tokens += estimate_tokens(&app.system_prompt.content);
    }

    for message in app.conversation_messages() {
        // Base content tokens
        let content_tokens = estimate_tokens(&message.content);
        total_estimated_tokens += content_tokens;

        match &message.message_type {
            llm::chat::MessageType::ToolResult(results) => {
                tool_result_count += 1;
                // Count tool result payload tokens from the results[].function.arguments JSON
                let mut tool_tokens = 0usize;
                for call in results {
                    tool_tokens += estimate_tokens(&call.function.arguments);
                }
                tool_estimated_tokens += tool_tokens;
                total_estimated_tokens += tool_tokens;
            }
            llm::chat::MessageType::ToolUse(calls) => {
                tool_use_count += 1;
                // Count tool use payload tokens from the calls[].function.arguments JSON
                let mut tool_tokens = 0usize;
                for call in calls {
                    tool_tokens += estimate_tokens(&call.function.arguments);
                }
                tool_estimated_tokens += tool_tokens;
                total_estimated_tokens += tool_tokens;
            }
            _ => {}
        }

        match message.role {
            ChatRole::User => user_message_count += 1,
            ChatRole::Assistant => assistant_message_count += 1,
        }
    }

    let total_message_count = user_message_count + assistant_message_count;

    let percentage_used = if model_context_window > 0 {
        (total_estimated_tokens * 100) / model_context_window
    } else {
        0
    };

    use num_format::{Locale, ToFormattedString};

    let model_context_window_formatted = model_context_window.to_formatted_string(&Locale::en);
    let total_estimated_tokens_formatted = total_estimated_tokens.to_formatted_string(&Locale::en);
    let tool_estimated_tokens_formatted = tool_estimated_tokens.to_formatted_string(&Locale::en);
    let last_response_usage_tokens_formatted =
        last_response_usage_tokens.to_formatted_string(&Locale::en);

    let yolo_mode = app.yolo_mode;
    let system_prompt_status = if app.cli_args.no_system_prompt {
        "disabled"
    } else {
        "enabled"
    };

    let info_text = format!(
        r#"Session ID: {session_id}
Working directory: {working_dir}
{lint_command_info}
{edit_tool_info}
System prompt: {system_prompt_status}
Total messages: {total_message_count} (User: {user_message_count}, Assistant: {assistant_message_count}, ToolUse: {tool_use_count}, ToolResult: {tool_result_count})
Yolo mode: {yolo_mode}

Model name: {model_name}
Model context: {model_context_window_formatted}

API tokens: {last_response_usage_tokens_formatted}
Estimated Tokens: {total_estimated_tokens_formatted} (tool: {tool_estimated_tokens_formatted})
% used of context: {percentage_used}%"#
    );

    print_command(info_text);
}

pub struct InfoCommand;

impl DotCommand for InfoCommand {
    fn name(&self) -> &'static str {
        ".info"
    }
    fn description(&self) -> &'static str {
        "Show information about the current session and model"
    }
    fn execute<'a>(
        &'a self,
        ctx: CommandContext<'a>,
    ) -> Pin<Box<dyn Future<Output = DotCommandResult> + Send + 'a>> {
        Box::pin(async move {
            handle_info_command(ctx.app_arc);
            DotCommandResult::Continue
        })
    }
}
