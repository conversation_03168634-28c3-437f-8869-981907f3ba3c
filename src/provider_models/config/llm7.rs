use crate::provider_models::common_config::{LlmProviderConfig, RetryInfoDisplay};
use crate::provider_models::llm_provider_type::LlmProviderType;
use crate::provider_models::serializable_llm_backend::SerializableLLMBackend;

pub fn get_deepseek_r1_0528_config() -> LlmProviderConfig {
    LlmProviderConfig {
        context_window_size: 131_072,
        api_model_name: "deepseek-r1-0528".to_string(),
        startup_banner_text: LlmProviderType::Llm7DeepSeek_R1_0528
            .get_startup_banner_text()
            .to_string(),
        api_key_env_names: vec!["LLM7_API_KEY".to_string()],
        backend: SerializableLLMBackend::OpenAI,
        base_url: Some("https://api.llm7.io/v1/".to_string()),
        retry_on_api_error: false,
        retry_info_display: RetryInfoDisplay::ShowFull,
        is_github_copilot: false,
        is_base_url_absolute: false,
    }
}

pub fn get_grok_3_mini_high_config() -> LlmProviderConfig {
    LlmProviderConfig {
        context_window_size: 128_000,
        api_model_name: "grok-3-mini-high".to_string(),
        startup_banner_text: LlmProviderType::Llm7Grok3MiniHigh
            .get_startup_banner_text()
            .to_string(),
        api_key_env_names: vec!["LLM7_API_KEY".to_string()],
        backend: SerializableLLMBackend::OpenAI,
        base_url: Some("https://api.llm7.io/v1/".to_string()),
        retry_on_api_error: false,
        retry_info_display: RetryInfoDisplay::ShowFull,
        is_github_copilot: false,
        is_base_url_absolute: false,
    }
}
