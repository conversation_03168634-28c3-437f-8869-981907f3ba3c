use crate::provider_models::common_config::{LlmProviderConfig, RetryInfoDisplay};
use crate::provider_models::llm_provider_type::LlmProviderType;
use crate::provider_models::serializable_llm_backend::SerializableLLMBackend;

const ENV_KEY_NAMES: [&str; 4] = [
    "GOOGLE_GEMINI_API_KEY",
    "GOOGLE_GEMINI_API_KEY2",
    "GOOGLE_GEMINI_API_KEY3",
    "GOOGLE_GEMINI_API_KEY4",
];

pub fn get_gemini_2_0_flash_config() -> LlmProviderConfig {
    LlmProviderConfig {
        context_window_size: 1_000_000,
        api_model_name: "gemini-2.0-flash".to_string(),
        startup_banner_text: LlmProviderType::GoogleGemini2_0Flash
            .get_startup_banner_text()
            .to_string(),
        api_key_env_names: ENV_KEY_NAMES.iter().map(|s| s.to_string()).collect(),
        backend: SerializableLLMBackend::Google,
        base_url: None,
        retry_on_api_error: false,
        retry_info_display: RetryInfoDisplay::ShowFull,
        is_github_copilot: false,
        is_base_url_absolute: false,
    }
}

pub fn get_gemini_2_5_flash_config() -> LlmProviderConfig {
    LlmProviderConfig {
        context_window_size: 1_000_000,
        api_model_name: "gemini-2.5-flash".to_string(),
        startup_banner_text: LlmProviderType::GoogleGemini2_5Flash
            .get_startup_banner_text()
            .to_string(),
        api_key_env_names: ENV_KEY_NAMES.iter().map(|s| s.to_string()).collect(),
        backend: SerializableLLMBackend::Google,
        base_url: None,
        retry_on_api_error: false,
        retry_info_display: RetryInfoDisplay::ShowFull,
        is_github_copilot: false,
        is_base_url_absolute: false,
    }
}

pub fn get_gemini_2_5_pro_config() -> LlmProviderConfig {
    LlmProviderConfig {
        context_window_size: 1_000_000,
        api_model_name: "gemini-2.5-pro".to_string(),
        startup_banner_text: LlmProviderType::GoogleGemini2_5Pro
            .get_startup_banner_text()
            .to_string(),
        api_key_env_names: ENV_KEY_NAMES.iter().map(|s| s.to_string()).collect(),
        backend: SerializableLLMBackend::Google,
        base_url: None,
        retry_on_api_error: false,
        retry_info_display: RetryInfoDisplay::ShowFull,
        is_github_copilot: false,
        is_base_url_absolute: false,
    }
}
