use crate::provider_models::common_config::{LlmProviderConfig, RetryInfoDisplay};
use crate::provider_models::llm_provider_type::LlmProviderType;
use crate::provider_models::serializable_llm_backend::SerializableLLMBackend;
use std::env;

pub fn get_deepseek_r1_0528_config() -> LlmProviderConfig {
    let endpoint = env::var("VERTEX_AI_ENDPOINT");
    let region = env::var("VERTEX_AI_REGION");
    let project_id = env::var("VERTEX_AI_PROJECT_ID");

    let base_url = match (endpoint, region, project_id) {
        (Ok(endpoint), Ok(region), Ok(project_id)) => Some(format!(
            "https://{endpoint}/v1/projects/{project_id}/locations/{region}/endpoints/openapi/"
        )),
        _ => {
            eprintln!("Error: Missing one or more Vertex AI environment variables (VERTEX_AI_ENDPOINT, VERTEX_AI_REGION, VERTEX_AI_PROJECT_ID). Using dummy URL.");
            Some("http://dummy.url".to_string())
        }
    };

    LlmProviderConfig {
        context_window_size: 130_000,
        api_model_name: "deepseek-ai/deepseek-r1-0528-maas".to_string(),
        startup_banner_text: LlmProviderType::VertexAiDeepSeek_R1_0528
            .get_startup_banner_text()
            .to_string(),
        api_key_env_names: vec!["VERTEX_AI_AUTHORIZATION".to_string()],
        backend: SerializableLLMBackend::OpenAI,
        base_url,
        retry_on_api_error: false,
        retry_info_display: RetryInfoDisplay::ShowFull,
        is_github_copilot: false,
        is_base_url_absolute: false,
    }
}
