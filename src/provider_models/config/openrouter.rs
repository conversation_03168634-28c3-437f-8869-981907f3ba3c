use crate::provider_models::common_config::{LlmProviderConfig, RetryInfoDisplay};
use crate::provider_models::llm_provider_type::LlmProviderType;
use crate::provider_models::serializable_llm_backend::SerializableLLMBackend;

pub fn get_mistral_small_3_2_24b_instruct_config() -> LlmProviderConfig {
    LlmProviderConfig {
        context_window_size: 96_000,
        api_model_name: "mistralai/mistral-small-3.2-24b-instruct:free".to_string(),
        startup_banner_text: LlmProviderType::OpenRouterMistralSmall3_2_24BInstruct
            .get_startup_banner_text()
            .to_string(),
        api_key_env_names: vec!["OPENROUTER_API_KEY".to_string()],
        backend: SerializableLLMBackend::OpenAI,
        base_url: Some("https://openrouter.ai/api/v1/".to_string()),
        retry_on_api_error: false,
        retry_info_display: RetryInfoDisplay::ShowFull,
        is_github_copilot: false,
        is_base_url_absolute: false,
    }
}

pub fn get_moonshotai_kimi_dev_72b_config() -> LlmProviderConfig {
    LlmProviderConfig {
        context_window_size: 131_072,
        api_model_name: "moonshotai/kimi-dev-72b:free".to_string(),
        startup_banner_text: LlmProviderType::OpenRouterMoonshotAiKimiDev72B
            .get_startup_banner_text()
            .to_string(),
        api_key_env_names: vec!["OPENROUTER_API_KEY".to_string()],
        backend: SerializableLLMBackend::OpenAI,
        base_url: Some("https://openrouter.ai/api/v1/".to_string()),
        retry_on_api_error: false,
        retry_info_display: RetryInfoDisplay::ShowFull,
        is_github_copilot: false,
        is_base_url_absolute: false,
    }
}

pub fn get_moonshotai_kimi_k2_config() -> LlmProviderConfig {
    LlmProviderConfig {
        context_window_size: 65_536,
        api_model_name: "moonshotai/kimi-k2:free".to_string(),
        startup_banner_text: LlmProviderType::OpenRouterMoonshotAiKimiK2
            .get_startup_banner_text()
            .to_string(),
        api_key_env_names: vec!["OPENROUTER_API_KEY".to_string()],
        backend: SerializableLLMBackend::OpenAI,
        base_url: Some("https://openrouter.ai/api/v1/".to_string()),
        retry_on_api_error: false,
        retry_info_display: RetryInfoDisplay::ShowFull,
        is_github_copilot: false,
        is_base_url_absolute: false,
    }
}

pub fn get_openrouter_horizon_beta_config() -> LlmProviderConfig {
    LlmProviderConfig {
        context_window_size: 256_000,
        api_model_name: "openrouter/horizon-beta".to_string(),
        startup_banner_text: LlmProviderType::OpenRouterHorizonBeta
            .get_startup_banner_text()
            .to_string(),
        api_key_env_names: vec!["OPENROUTER_API_KEY".to_string()],
        backend: SerializableLLMBackend::OpenAI,
        base_url: Some("https://openrouter.ai/api/v1/".to_string()),
        retry_on_api_error: false,
        retry_info_display: RetryInfoDisplay::ShowFull,
        is_github_copilot: false,
        is_base_url_absolute: false,
    }
}
