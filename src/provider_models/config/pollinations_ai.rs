use crate::provider_models::common_config::{LlmProviderConfig, RetryInfoDisplay};
use crate::provider_models::llm_provider_type::LlmProviderType;
use crate::provider_models::serializable_llm_backend::SerializableLLMBackend;

// All context window sizes are just guessed here since it falls back to gpt-4.1-nano-2025-04-14 anyway.

pub fn get_gpt4_1_config() -> LlmProviderConfig {
    LlmProviderConfig {
        context_window_size: 200_000,
        api_model_name: "gpt-4.1".to_string(),
        startup_banner_text: LlmProviderType::PollinationsAiGpt4_1
            .get_startup_banner_text()
            .to_string(),
        api_key_env_names: vec!["POLLINATIONS_AI_API_KEY".to_string()],
        backend: SerializableLLMBackend::OpenAI,
        base_url: Some("https://text.pollinations.ai/openai".to_string()),
        retry_on_api_error: true,
        retry_info_display: RetryInfoDisplay::ShowShort,
        is_github_copilot: false,
        is_base_url_absolute: true,
    }
}

pub fn get_o3_config() -> LlmProviderConfig {
    LlmProviderConfig {
        context_window_size: 200_000,
        api_model_name: "o3".to_string(),
        startup_banner_text: LlmProviderType::PollinationsAiGpt_o3
            .get_startup_banner_text()
            .to_string(),
        api_key_env_names: vec!["POLLINATIONS_AI_API_KEY".to_string()],
        backend: SerializableLLMBackend::OpenAI,
        base_url: Some("https://text.pollinations.ai/openai".to_string()),
        retry_on_api_error: true,
        retry_info_display: RetryInfoDisplay::ShowShort,
        is_github_copilot: false,
        is_base_url_absolute: true,
    }
}

pub fn get_deepseek_v3_config() -> LlmProviderConfig {
    LlmProviderConfig {
        context_window_size: 128_000,
        api_model_name: "deepseek-v3".to_string(),
        startup_banner_text: LlmProviderType::PollinationsAiDeepSeekV3
            .get_startup_banner_text()
            .to_string(),
        api_key_env_names: vec!["POLLINATIONS_AI_API_KEY".to_string()],
        backend: SerializableLLMBackend::OpenAI,
        base_url: Some("https://text.pollinations.ai/openai".to_string()),
        retry_on_api_error: true,
        retry_info_display: RetryInfoDisplay::ShowShort,
        is_github_copilot: false,
        is_base_url_absolute: true,
    }
}

pub fn get_deepseek_r1_0528_config() -> LlmProviderConfig {
    LlmProviderConfig {
        context_window_size: 128_000,
        api_model_name: "deepseek-r1".to_string(),
        startup_banner_text: LlmProviderType::PollinationsAiDeepSeekR1_0528
            .get_startup_banner_text()
            .to_string(),
        api_key_env_names: vec!["POLLINATIONS_AI_API_KEY".to_string()],
        backend: SerializableLLMBackend::OpenAI,
        base_url: Some("https://text.pollinations.ai/openai".to_string()),
        retry_on_api_error: true,
        retry_info_display: RetryInfoDisplay::ShowShort,
        is_github_copilot: false,
        is_base_url_absolute: true,
    }
}

pub fn get_grok_3_mini_config() -> LlmProviderConfig {
    LlmProviderConfig {
        context_window_size: 131_072,
        api_model_name: "grok-3-mini".to_string(),
        startup_banner_text: LlmProviderType::PollinationsAiGrok3Mini
            .get_startup_banner_text()
            .to_string(),
        api_key_env_names: vec!["POLLINATIONS_AI_API_KEY".to_string()],
        backend: SerializableLLMBackend::OpenAI,
        base_url: Some("https://text.pollinations.ai/openai".to_string()),
        retry_on_api_error: true,
        retry_info_display: RetryInfoDisplay::ShowShort,
        is_github_copilot: false,
        is_base_url_absolute: true,
    }
}

pub fn get_mistral_small_3_1_24b_instruct_config() -> LlmProviderConfig {
    LlmProviderConfig {
        context_window_size: 96_000,
        api_model_name: "mistral-small-3.1-24b-instruct".to_string(),
        startup_banner_text: LlmProviderType::PollinationsAiMistralSmall3_1_24b_instruct
            .get_startup_banner_text()
            .to_string(),
        api_key_env_names: vec!["POLLINATIONS_AI_API_KEY".to_string()],
        backend: SerializableLLMBackend::OpenAI,
        base_url: Some("https://text.pollinations.ai/openai".to_string()),
        retry_on_api_error: true,
        retry_info_display: RetryInfoDisplay::ShowShort,
        is_github_copilot: false,
        is_base_url_absolute: true,
    }
}
