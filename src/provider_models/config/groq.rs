use crate::provider_models::common_config::{LlmProviderConfig, RetryInfoDisplay};
use crate::provider_models::llm_provider_type::LlmProviderType;
use crate::provider_models::serializable_llm_backend::SerializableLLMBackend;

pub fn get_compound_beta_config() -> LlmProviderConfig {
    LlmProviderConfig {
        context_window_size: 70_000,
        api_model_name: "compound-beta".to_string(),
        startup_banner_text: LlmProviderType::GroqCompoundBeta
            .get_startup_banner_text()
            .to_string(),
        api_key_env_names: vec!["GROQ_API_KEY".to_string()],
        backend: SerializableLLMBackend::OpenAI,
        base_url: Some("https://api.groq.com/openai/v1/".to_string()),
        retry_on_api_error: false,
        retry_info_display: RetryInfoDisplay::ShowFull,
        is_github_copilot: false,
        is_base_url_absolute: false,
    }
}

pub fn get_kimi_k2_instruct_config() -> LlmProviderConfig {
    LlmProviderConfig {
        context_window_size: 10_000,
        api_model_name: "moonshotai/kimi-k2-instruct".to_string(),
        startup_banner_text: LlmProviderType::GroqMoonshotAiKimiK2Instruct
            .get_startup_banner_text()
            .to_string(),
        api_key_env_names: vec!["GROQ_API_KEY".to_string()],
        backend: SerializableLLMBackend::OpenAI,
        base_url: Some("https://api.groq.com/openai/v1/".to_string()),
        retry_on_api_error: false,
        retry_info_display: RetryInfoDisplay::ShowFull,
        is_github_copilot: false,
        is_base_url_absolute: false,
    }
}
