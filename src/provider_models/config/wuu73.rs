use crate::provider_models::common_config::{LlmProviderConfig, RetryInfoDisplay};
use crate::provider_models::llm_provider_type::LlmProviderType;
use crate::provider_models::serializable_llm_backend::SerializableLLMBackend;

pub fn get_wuu73_kimi_k2_config() -> LlmProviderConfig {
    LlmProviderConfig {
        context_window_size: 32_768,
        api_model_name: "o-moonshotai/kimi-k2".to_string(),
        startup_banner_text: LlmProviderType::Wuu73KimiK2
            .get_startup_banner_text()
            .to_string(),
        api_key_env_names: vec![],
        backend: SerializableLLMBackend::OpenAI,
        base_url: Some("https://extra.wuu73.org/api/v1/".to_string()),
        retry_on_api_error: false,
        retry_info_display: RetryInfoDisplay::ShowFull,
        is_github_copilot: false,
        is_base_url_absolute: false,
    }
}

pub fn get_wuu73_qwen_coder_config() -> LlmProviderConfig {
    LlmProviderConfig {
        context_window_size: 262_144,
        api_model_name: "o-qwen/qwen3-coder".to_string(),
        startup_banner_text: LlmProviderType::Wuu73Qwen3Coder
            .get_startup_banner_text()
            .to_string(),
        api_key_env_names: vec![],
        backend: SerializableLLMBackend::OpenAI,
        base_url: Some("https://extra.wuu73.org/api/v1/".to_string()),
        retry_on_api_error: false,
        retry_info_display: RetryInfoDisplay::ShowFull,
        is_github_copilot: false,
        is_base_url_absolute: false,
    }
}

pub fn get_wuu73_qwen_235b_a22b_config() -> LlmProviderConfig {
    LlmProviderConfig {
        context_window_size: 131_072,
        api_model_name: "o-qwen/qwen3-235b-a22b".to_string(),
        startup_banner_text: LlmProviderType::Wuu73Qwen3_235B_A22B
            .get_startup_banner_text()
            .to_string(),
        api_key_env_names: vec![],
        backend: SerializableLLMBackend::OpenAI,
        base_url: Some("https://extra.wuu73.org/api/v1/".to_string()),
        retry_on_api_error: false,
        retry_info_display: RetryInfoDisplay::ShowFull,
        is_github_copilot: false,
        is_base_url_absolute: false,
    }
}
