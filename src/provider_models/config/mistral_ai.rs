use crate::provider_models::common_config::{LlmProviderConfig, RetryInfoDisplay};
use crate::provider_models::llm_provider_type::LlmProviderType;
use crate::provider_models::serializable_llm_backend::SerializableLLMBackend;

pub fn get_codestral_config() -> LlmProviderConfig {
    LlmProviderConfig {
        context_window_size: 256_000,
        api_model_name: "codestral-latest".to_string(),
        startup_banner_text: LlmProviderType::MistralAiCodestral
            .get_startup_banner_text()
            .to_string(),
        api_key_env_names: vec!["MISTRAL_CODESTRAL_API_KEY".to_string()],
        backend: SerializableLLMBackend::OpenAI,
        base_url: Some("https://codestral.mistral.ai/v1/".to_string()),
        retry_on_api_error: false,
        retry_info_display: RetryInfoDisplay::ShowFull,
        is_github_copilot: false,
        is_base_url_absolute: false,
    }
}
