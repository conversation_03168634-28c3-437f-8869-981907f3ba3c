use crate::provider_models::llm_provider_type::LlmProviderType;
use crossterm::style::Stylize;
use std::path::Path;

/// Prints a nicely formatted startup banner with agent info and session details
pub fn print_startup_banner(
    cwd: &Path,
    proxy: Option<u16>,
    current_model: LlmProviderType,
    has_custom_user_prompt: bool,
    restore_info: Option<String>,
) {
    let mut info_parts = Vec::new();
    if let Some(port) = proxy {
        info_parts.push(format!("proxy:{port}"));
    }
    println!(
        "\nDima AI Agent     {}    {}\n\nWorking in {}",
        current_model.get_config().startup_banner_text,
        if info_parts.is_empty() {
            "".to_string()
        } else {
            info_parts.join(", ").yellow().to_string()
        },
        shorten_path(cwd).to_string().blue()
    );
    if has_custom_user_prompt {
        println!("Using dima-agent.md");
    }
    if let Some(info) = restore_info {
        println!("\n{info}");
    }
    println!();
}

fn shorten_path(path: &Path) -> String {
    if let Some(home_dir) = dirs::home_dir() {
        if let Ok(stripped_path) = path.strip_prefix(&home_dir) {
            return format!("~/{}", stripped_path.display());
        }
    }
    path.display().to_string()
}
