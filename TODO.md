# implement new flag to add estimated token count for every tool use, like searching, the estimated token should show how large the tool request+response tokens are, since they can get quite large if AI searches incorrectly. use the nice token display

# fix Wuu73 response for qwen models always failing when tools are used

I tried to use `"content": ""`, but it still just fails.

Needs response body investigation.

# Aider mode

One of the big issue is file management, commands like .drop-all, .files, and so on. Also automated file path injection is different in aider, it only checks file paths starting with /, while my non aider mode does not use / at start.

# Repo Map

I have a semi working Rust repo for this:
https://github.com/Dima-369/rust-cli-aider-repo-map

I think it is only included once at start? With double size on first message only

---

implement --aider

.info shows this info, it is a boolean

another dot command to toggle it, like .aider

When enabled, do NOT send any tools, so LLMBuilder needs to be rebuilt without any tools send. Also different system prompt, see below.

When you first start without aider mode, use tools, and then switch to aider mode, the session history still needs to include the old tools, but they are simply not sent to API, and on switching out of aider mode, you again start sending used tools.

## If the AI response includes file paths, like README.md or src/main.rs you need to 

Attached files need to be stored as a list inside the session directory, but are only relevant for aider mode.

## Every model in LlmProviderConfig needs a new aider-edit-mode. It has 3 values:

Whole is currently not set in those configs, but implement. Use diff format for all models, except the Google Gemini models, those use diff-fenced.

You can force the whole edit format only by using a new flag --aider-whole-edit-mode.

Every single one of those 3 diff modes, needs to a specific system prompt set, see below.

### whole

The “whole” edit format is the simplest possible editing format. The LLM is instructed to return a full, updated copy of each source file that needs changes. While simple, it can be slow and costly because the LLM has to return the entire file even if just a few lines are edited.

The whole format expects the file path just before the fenced file content:

show_greeting.py
```
import sys

def greeting(name):
    print("Hey", name)

if __name__ == '__main__':
    greeting(sys.argv[1])
```

### diff

The “diff” edit format asks the LLM to specify file edits as a series of search/replace blocks. This is an efficient format, because the model only needs to return parts of the file which have changes.

Edits are formatted using a syntax similar to the git merge conflict resolution markings, with the file path right before a fenced block:

mathweb/flask/app.py
```
<<<<<<< SEARCH
from flask import Flask
=======
import math
from flask import Flask
>>>>>>> REPLACE
```

### diff-fenced

The “diff-fenced” edit format is based on the diff format, but the file path is placed inside the fence. It is primarily used with the Gemini family of models, which often fail to conform to the fencing approach specified in the diff format.

```
mathweb/flask/app.py
<<<<<<< SEARCH
from flask import Flask
=======
import math
from flask import Flask
>>>>>>> REPLACE
```

