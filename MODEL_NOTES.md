# Notes

Note that my implementation has smart API key cycling, so if you have multiple `GOOGLE_GEMINI_API_KEY` keys,
read `src/provider_models/config/google_gemini.rs` and related files on the environment variable names to use for the
different keys.

All tests below are for `OnlyWriteFile` if not otherwise specified.

# GitHub Copilot (✅)

The free tier has 500 free requests every month across many models, including Claude 3.5 Sonnet.

You can use the `reauth-github-copilot` command to switch to a different account for more access.

Not sure if only Claude 3.5 Sonnet is so slow, maybe the other models are quicker.

## Claude 3.5 Sonnet (✅ good, but slow)

It is really slow to respond. It might be intentionally throttled.

# <PERSON> (✅ good)

Requires the `GOOGLE_GEMINI_API_KEY` environment variable to be set.

I rate this good, but it is infuriating seeing how badly they perform sometimes.

If the Flash models start asking too many questions, just tell them to stop and finish the task.
That's because of my system prompt, it is not that way in Gemini CLI.

## Gemini 2.5 Flash (✅ good)

This is pretty good on Rust/Kolin but sometimes terrible at editing files.
It sometimes incorrectly quotes inside strings which is annoying.
Has a tendency to write a file, then open it multiple times, or writing with same content.

Even in the Gemini CLI, it keeps on failing edits and opening non-existing files.

On Lua, for editing files, it often breaks functions because it replaces `end` with `}` and incorrectly quotes which is
no fun.

## Gemini 2.0 Flash (✅ fine)

Way worse than Gemini 2.5 Flash actually, but still alright.

# LLM7

None seem to have tool support?

## Deepseek R1/grok-3-mini-high (❌ no tool use)

Tool use is entirely ignored.

# Together (✅ fine)

## DeepSeek V3 (🤨 good, but slow)

It is very slow sometimes.

Needs more testing, but looks fine so far.

# Mistral Codestral (✅ a bit slow, but okay)

Not as good as the Google Gemini models, might be on the 2.0 Flash level for some tasks, a lot worse for others.

It sometimes struggles a lot with Rust and string types.

I tested with the Gemini system prompt, and I think it is just too complex for it, it performs worse that way.

# Cohere (❌ bad models)

## Command A-03 2025  (❌ not smart enough)

I saw it incorrectly inserting `);` into a string on an unrelated line on writing a whole file.
I told it to review the file, and it did not pick up on that. `cargo check` still passed because the changes were inside
a string.

Might be a bit smarter than `Command R-08 2024`, but it still performs really badly in this agentic workflow.

## Command R-08 2024 (❌too slow and just bad)

It is slow, and often really stupid, does not navigate code-base.

Can fail with 422 with this error which goes away on retrying.

```json
{
  "id": "91525018-5921-4b6e-bc84-9c1aaa7f0674",
  "message": "your request resulted in an invalid tool generation. Try updating the messages or tool definitions"
}
```

It keeps getting 'stuck' by writing `I will now ...`, but forgets to call tools, so the loop is broken.

# OpenRouter

The Stealth models are really good sometimes, check: https://openrouter.ai/provider/stealth

Those do not support tool use:

- deepseek/deepseek-r1-0528:free
- qwen3-235b-a22b:free
- moonshotai/kimi-dev-72b:free

Those just result in 429:

- moonshotai/kimi-k2:free

# Wuu73

Wraps OpenRouter, Pollinations AI and LLM7.

## Qwen 3 Coder / Qwen 3 235B (OpenRouter)

As soon as any tools are called, it fails like this:

```
Error: Response Format Error: OpenAI API returned error status: 422 Unprocessable Entity. Raw response: {"detail":[{"typ
e":"missing","loc":["body","messages",4,"content"],"msg":"Field required","input":{"role":"assistant","tool_calls":[{"id
":"call_331b0a5b04cf48f3aa9b9837","type":"function","function":{"name":"bash","arguments":"{\"command\": \"ls -la | grep
 -i readme\"}"}}]}}]}
 ```

## Kimi-K2 (OpenRouter)

Fails with 429/500 pretty much all the time:

```
{
  "detail": "429: {\"error\":{\"message\":\"Provider returned error\",\"code\":429,\"metadata\":{\"raw\":\"moonshotai/kimi-k2:free is temporarily rate-limited upstream. Please retry shortly, or add your own key to accumulate your rate limits: https://openrouter.ai/settings/integrations\",\"provider_name\":\"Chutes\"}},\"user_id\":\"user_2nE1kab7pd1Phy1LCbaVnzWIF2w\"}"
}
```

## Mistral 3.1 24B Instruct / Mistral 3.2 24B Instruct

Just broken, always fail with this, although 200 it returns a status code.

Other free models like `deepseek/deepseek-chat:free` work just fine, although they have no tool support.

```json
{
  "error": {
    "message": "Internal Server Error",
    "code": 500
  },
  "user_id": "user_2towirRZERkEs9tld9TtQCTZNHb"
}
```

# gpt4free (❌ not recommended for tool use)

https://github.com/xtekky/gpt4free

I am not able to find a stable model and provider that provides tool use.

The repo below has tested all interesting models, and there are only 3 that provide tools, and those are unreliable, as
well.
Without tool use, there are good models though, like o3, deepseek, grok models.

https://github.com/Dima-369/gpt4free-python-ai-llm-model-provider-tester-system-tools

# Nvidia AI

I only tested with Kimi K2 Instruct, and noticed that it sometimes takes about a minute or longer to respond, or sometimes disconnects which is not fun.

## Kimi K2 Instruct (❌ bad)

This feels dumb, not like the real model, maybe it is quantized or something.
I compared this model to the Groq's version, and this one is awful.

# Groq

## Groq Compound (❌ does not support tool use)

## Kimi K2 Instruct (🤨 very limited context window, otherwise good)

It has extremely quick response times and is very good!

This is my go-to model for tasks with a low context window.

The limited 10k tokens context window and 10k tokens per minute make it a bit annoying to work with. It still performs quite well, but probably not on the level of Claude models.

# Pollinations.AI (❌ unreliable)

Often has network issues which go away on retrying, sometimes on retrying a lot.
It also shows ads inside the responses, even though I disabled it in https://auth.pollinations.ai

On requesting o3, DeepSeek, grok or GPT-4.1 models, very often, the model is instead just `gpt-4.1-nano-2025-04-14`.

Interestingly, gpt4free also uses Pollinations.AI, and receives the real models. It uses
`POST https://text.pollinations.ai/openai`, unlike the official documentation.
And with that I sometimes also get a `grok-3-mini` model, but only as long as the
context window is very small. As soon as it increases, it either falls back to nano or reports the error below:

```json
{
  "error": "Model not found or tier not high enough. Your tier: anonymous, required tier: seed. To get a token or add a referrer, visit https://auth.pollinations.ai",
  "status": 402
}
```

## GPT-4.1 nano (🤨 okay)

Surprisingly, it can perform fine, on editing whole files sometimes, but often times it just messes them up.
It is really nice though, when it just needs to run some bash commands, since it responds quick.

This model messed up the `override` boolean parameter in the `write_file` tool so many times, that I disabled it by
default.

It often messes up the `find_and_replace_code` tool and does not even understand it, when the tool reports failure.

# Google Vertex.AI (❌ token expires too often)

Very often, the token behind `VERTEX_AI_AUTHORIZATION` expires and needs to be refreshed,
which is super annoying. I think every hour?

You need to run `gcloud auth print-access-token` to get a new token. I do it in the Cloud Shell.
Maybe there is a better way?

## Deepseek-R1-0528 (❌ tool uses fail)

As soon as any tool responses are used in the request body, it fails with `500: Internal error encountered.`,
rendering this useless without `--aider`.

Also, the response body does not differentiate between `content` and `reasoning_content`.
The chain of thought (COT) is not supposed to be appended to the assistant messages, but it is impossible to distinguish
between the two reliably because the COT is not in a separate message.

# Kluster.ai

https://platform.kluster.ai/plans

Has those free limits making it impractical for many tasks because the context window is too small,
even if the model is good.

- Requests per minute: 30
- Context window: Up to 32K tokens
- Max output: Up to 4K tokens

# Inference.net

https://inference.net/

Free credits: $1, $25 on responding to an email survey

Does not have any interesting models for this use-case.
